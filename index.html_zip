<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示站点导航 - Demo Sites Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 210, 211, 0.15) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            transform: perspective(1000px) rotateX(10deg);
        }

        .header h1 {
            font-size: 3.5rem;
            color: white;
            text-shadow: 0 10px 20px rgba(0,0,0,0.3);
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #e0e0e0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .demo-card {
            background: rgba(255,255,255,0.12);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            position: relative;
            transform: perspective(1000px) rotateX(5deg) rotateY(-2deg);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(255,255,255,0.25);
            box-shadow:
                0 20px 40px rgba(0,0,0,0.15),
                0 0 0 1px rgba(255,255,255,0.15),
                inset 0 1px 0 rgba(255,255,255,0.25);
            cursor: pointer;
            overflow: hidden;
        }

        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .demo-card:hover::before {
            left: 100%;
        }

        .demo-card:hover {
            transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(-15px) scale(1.03);
            box-shadow:
                0 35px 70px rgba(0,0,0,0.25),
                0 0 0 1px rgba(255,255,255,0.35),
                inset 0 1px 0 rgba(255,255,255,0.35);
            background: rgba(255,255,255,0.18);
        }

        .demo-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 0 10px 20px rgba(238, 90, 36, 0.3);
            transform: translateZ(20px);
        }

        .demo-card:nth-child(2n) .demo-icon {
            background: linear-gradient(135deg, #4834d4, #686de0);
            box-shadow: 0 10px 20px rgba(72, 52, 212, 0.3);
        }

        .demo-card:nth-child(3n) .demo-icon {
            background: linear-gradient(135deg, #00d2d3, #54a0ff);
            box-shadow: 0 10px 20px rgba(0, 210, 211, 0.3);
        }

        .demo-card:nth-child(4n) .demo-icon {
            background: linear-gradient(135deg, #5f27cd, #a55eea);
            box-shadow: 0 10px 20px rgba(95, 39, 205, 0.3);
        }

        .demo-title {
            font-size: 1.5rem;
            color: white;
            margin-bottom: 10px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .demo-description {
            color: rgba(255,255,255,0.8);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .demo-url {
            color: rgba(255,255,255,0.6);
            font-size: 0.85rem;
            font-family: 'Courier New', monospace;
            background: rgba(0,0,0,0.2);
            padding: 8px 12px;
            border-radius: 8px;
            display: inline-block;
            margin-bottom: 15px;
        }

        .visit-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
            text-decoration: none;
            display: inline-block;
        }

        .visit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.6);
        }

        .nav-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255,255,255,0.12);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255,255,255,0.25);
            padding: 15px 0;
            z-index: 1000;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .logo-img {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 30px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 20px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .demo-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }

        .info-section {
            background: rgba(0,0,0,0.25);
            padding: 15px;
            border-radius: 12px;
            border: 1px solid rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
        }

        .info-title {
            color: rgba(255,255,255,0.9);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .info-content {
            color: rgba(255,255,255,0.7);
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .qr-code {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 8px;
            margin: 10px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: #666;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .login-info {
            font-family: 'Courier New', monospace;
        }

        .demo-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            box-shadow: 0 3px 10px rgba(238, 90, 36, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #4834d4, #686de0);
            color: white;
            box-shadow: 0 3px 10px rgba(72, 52, 212, 0.4);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .btn-changelog {
            background: linear-gradient(135deg, #00d2d3, #54a0ff);
            color: white;
            box-shadow: 0 3px 10px rgba(0, 210, 211, 0.4);
        }

        .btn-features {
            background: linear-gradient(135deg, #5f27cd, #a55eea);
            color: white;
            box-shadow: 0 3px 10px rgba(95, 39, 205, 0.4);
            position: relative;
        }

        .features-panel {
            position: absolute;
            bottom: 100%;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.92);
            backdrop-filter: blur(20px);
            border-radius: 15px 15px 0 0;
            padding: 20px;
            transform: translateY(100%);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 15;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            opacity: 0;
            visibility: hidden;
            box-shadow: 0 -5px 20px rgba(0,0,0,0.3);
            margin-bottom: 5px;
        }

        .btn-features:hover .features-panel {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }

        .features-title {
            color: #00d2d3;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: left;
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .features-list li {
            color: rgba(255,255,255,0.8);
            font-size: 0.85rem;
            padding: 3px 0;
            text-align: left;
            position: relative;
            padding-left: 15px;
        }

        .features-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #00d2d3;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }

            .demo-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .demo-card {
                transform: none;
            }

            .demo-card:hover {
                transform: translateY(-5px) scale(1.01);
            }

            .nav-container {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                gap: 15px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .demo-info {
                grid-template-columns: 1fr;
            }

            .demo-actions {
                flex-direction: column;
            }

            .container {
                padding-top: 120px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="nav-header">
        <div class="nav-container">
            <a href="#" class="nav-logo">
                <div class="logo-img">🏢</div>
                <span>您的企业名称</span>
            </a>
            <div class="nav-links">
                <a href="https://your-website.com" class="nav-link" target="_blank">🏠 官网首页</a>
                <a href="https://forum.your-website.com" class="nav-link" target="_blank">💬 技术论坛</a>
                <a href="https://docs.your-website.com" class="nav-link" target="_blank">📚 帮助文档</a>
                <a href="mailto:<EMAIL>" class="nav-link">📧 联系我们</a>
            </div>
        </div>
    </div>

    <div class="container" style="padding-top: 100px;">
        <div class="header">
            <h1>演示站点导航</h1>
            <p>Demo Sites Portal - 点击下方卡片访问各个演示站点</p>
        </div>

        <div class="demo-grid" id="demoGrid">
            <!-- 示例演示站点 -->
            <div class="demo-card">
                <div class="demo-icon">🚀</div>
                <h3 class="demo-title">电商管理系统</h3>
                <p class="demo-description">功能完整的电商管理平台，包含商品管理、订单处理、用户管理等核心功能。</p>

                <div class="demo-info">
                    <div class="info-section">
                        <div class="info-title">🔐 后台登录</div>
                        <div class="info-content login-info">
                            账号: admin<br>
                            密码: 123456
                        </div>
                    </div>
                    <div class="info-section">
                        <div class="info-title">📱 小程序码</div>
                        <div class="qr-code">
                            扫码体验<br>小程序端
                        </div>
                    </div>
                </div>

                <div class="demo-actions">
                    <a href="https://demo1.example.com" class="action-btn btn-primary" target="_blank">访问前台</a>
                    <a href="https://admin.demo1.example.com" class="action-btn btn-secondary" target="_blank">管理后台</a>
                    <a href="https://changelog.demo1.example.com" class="action-btn btn-changelog" target="_blank">更新日志</a>
                    <div class="action-btn btn-features">
                        系统特色
                        <!-- 系统特色面板 -->
                        <div class="features-panel">
                            <div class="features-title">🌟 系统特色</div>
                            <ul class="features-list">
                                <li>支持多商户入驻管理</li>
                                <li>完整的订单流程处理</li>
                                <li>智能库存预警系统</li>
                                <li>多种支付方式集成</li>
                                <li>数据统计分析报表</li>
                                <li>移动端完美适配</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-card">
                <div class="demo-icon">💼</div>
                <h3 class="demo-title">企业OA系统</h3>
                <p class="demo-description">企业办公自动化系统，支持审批流程、考勤管理、文档协作等功能。</p>

                <div class="demo-info">
                    <div class="info-section">
                        <div class="info-title">🔐 测试账号</div>
                        <div class="info-content login-info">
                            管理员: manager<br>
                            密码: manager123<br>
                            员工: staff001<br>
                            密码: staff123
                        </div>
                    </div>
                    <div class="info-section">
                        <div class="info-title">🌐 访问地址</div>
                        <div class="info-content">
                            支持PC端和移动端<br>
                            响应式设计
                        </div>
                    </div>
                </div>

                <div class="demo-actions">
                    <a href="https://oa.example.com" class="action-btn btn-primary" target="_blank">立即体验</a>
                    <a href="https://changelog.oa.example.com" class="action-btn btn-changelog" target="_blank">更新日志</a>
                    <div class="action-btn btn-features">
                        系统特色
                        <!-- 系统特色面板 -->
                        <div class="features-panel">
                            <div class="features-title">🌟 系统特色</div>
                            <ul class="features-list">
                                <li>灵活的审批流程配置</li>
                                <li>多维度考勤统计</li>
                                <li>在线文档协作编辑</li>
                                <li>即时消息通讯</li>
                                <li>移动办公支持</li>
                                <li>权限精细化管理</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-card">
                <div class="demo-icon">🎨</div>
                <h3 class="demo-title">在线教育平台</h3>
                <p class="demo-description">完整的在线教育解决方案，包含课程管理、直播教学、作业系统等。</p>

                <div class="demo-info">
                    <div class="info-section">
                        <div class="info-title">👥 角色账号</div>
                        <div class="info-content login-info">
                            教师: teacher<br>
                            密码: teacher123<br>
                            学生: student<br>
                            密码: student123
                        </div>
                    </div>
                    <div class="info-section">
                        <div class="info-title">📱 移动端</div>
                        <div class="qr-code">
                            扫码下载<br>APP体验
                        </div>
                    </div>
                </div>

                <div class="demo-actions">
                    <a href="https://edu.example.com" class="action-btn btn-primary" target="_blank">学习平台</a>
                    <a href="https://teacher.edu.example.com" class="action-btn btn-secondary" target="_blank">教师端</a>
                    <a href="https://changelog.edu.example.com" class="action-btn btn-changelog" target="_blank">更新日志</a>
                    <div class="action-btn btn-features">
                        系统特色
                        <!-- 系统特色面板 -->
                        <div class="features-panel">
                            <div class="features-title">🌟 系统特色</div>
                            <ul class="features-list">
                                <li>高清直播互动教学</li>
                                <li>智能作业批改系统</li>
                                <li>学习进度跟踪分析</li>
                                <li>多媒体课件支持</li>
                                <li>在线考试评测</li>
                                <li>家校互动沟通</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-card">
                <div class="demo-icon">📊</div>
                <h3 class="demo-title">数据分析平台</h3>
                <p class="demo-description">企业级数据分析和可视化平台，支持多维度数据展示和智能报表生成。</p>

                <div class="demo-info">
                    <div class="info-section">
                        <div class="info-title">🔐 演示账号</div>
                        <div class="info-content login-info">
                            账号: demo<br>
                            密码: demo2024
                        </div>
                    </div>
                    <div class="info-section">
                        <div class="info-title">📈 功能特色</div>
                        <div class="info-content">
                            实时数据监控<br>
                            自定义报表<br>
                            数据导出
                        </div>
                    </div>
                </div>

                <div class="demo-actions">
                    <a href="https://analytics.example.com" class="action-btn btn-primary" target="_blank">查看演示</a>
                    <a href="https://changelog.analytics.example.com" class="action-btn btn-changelog" target="_blank">更新日志</a>
                    <div class="action-btn btn-features">
                        系统特色
                        <!-- 系统特色面板 -->
                        <div class="features-panel">
                            <div class="features-title">🌟 系统特色</div>
                            <ul class="features-list">
                                <li>实时数据监控大屏</li>
                                <li>多维度数据钻取</li>
                                <li>自定义报表设计</li>
                                <li>智能预警提醒</li>
                                <li>数据导出多格式</li>
                                <li>API接口开放</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 复制登录信息到剪贴板
        function copyLoginInfo(text) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('登录信息已复制到剪贴板');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('登录信息已复制到剪贴板');
            });
        }

        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                z-index: 10000;
                font-size: 14px;
                backdrop-filter: blur(10px);
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 2000);
        }

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'perspective(1000px) rotateX(5deg) rotateY(-2deg)';
                }, index * 100);
            });

            // 为登录信息添加点击复制功能
            document.querySelectorAll('.login-info').forEach(element => {
                element.style.cursor = 'pointer';
                element.title = '点击复制登录信息';
                element.addEventListener('click', () => {
                    copyLoginInfo(element.textContent.trim());
                });
            });

            // 添加导航栏滚动效果
            let lastScrollTop = 0;
            const navbar = document.querySelector('.nav-header');

            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // 向下滚动，隐藏导航栏
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    // 向上滚动，显示导航栏
                    navbar.style.transform = 'translateY(0)';
                }

                lastScrollTop = scrollTop;
            });
        });
    </script>
</body>
</html>
