<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示站点 - 系统入口</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
            min-height: 100vh;
            perspective: 1200px;
            overflow-x: hidden;
        }

        /* 装饰性元素 */
        body::before {
            content: '';
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 177, 153, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 226, 0.2) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* 导航栏 */
        .navbar {
            background: rgba(30, 30, 60, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 2px solid rgba(120, 119, 198, 0.3);
            padding: 1.5rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            font-size: 2rem;
            font-weight: 900;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 107, 107, 0.5);
            position: relative;
        }

        .logo::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            border-radius: 15px;
            opacity: 0.1;
            z-index: -1;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            list-style: none;
        }

        .nav-link {
            color: #e2e8f0;
            text-decoration: none;
            padding: 0.8rem 1.5rem;
            border-radius: 20px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: linear-gradient(145deg, rgba(60, 60, 120, 0.6), rgba(40, 40, 80, 0.6));
            border: 2px solid rgba(120, 119, 198, 0.3);
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover {
            background: linear-gradient(145deg, rgba(120, 119, 198, 0.8), rgba(80, 80, 160, 0.8));
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 25px rgba(120, 119, 198, 0.4);
            border-color: rgba(120, 119, 198, 0.8);
        }

        /* 主要内容区域 */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 4rem 2rem;
            position: relative;
        }

        .page-title {
            text-align: center;
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #f9ca24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 50px rgba(255, 107, 107, 0.3);
            position: relative;
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.3)); }
            to { filter: drop-shadow(0 0 40px rgba(78, 205, 196, 0.5)); }
        }

        .page-subtitle {
            text-align: center;
            color: rgba(226, 232, 240, 0.8);
            font-size: 1.3rem;
            margin-bottom: 4rem;
            font-weight: 300;
            letter-spacing: 0.5px;
        }

        /* 卡片网格 - 3x3布局 */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin-top: 2rem;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }

        /* 3D写实卡片样式 - 参考图片风格 */
        .demo-card {
            aspect-ratio: 1;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 25px;
            padding: 0;
            box-shadow:
                0 25px 50px rgba(0,0,0,0.25),
                0 15px 30px rgba(0,0,0,0.15),
                inset 0 2px 0 rgba(255,255,255,0.9),
                inset 0 -2px 0 rgba(0,0,0,0.1),
                inset 2px 0 0 rgba(255,255,255,0.5),
                inset -2px 0 0 rgba(0,0,0,0.05);
            transform: translateZ(0);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            border: 3px solid rgba(255,255,255,0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .demo-card::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border-radius: 20px;
            background: linear-gradient(145deg, transparent, rgba(255,255,255,0.1));
            pointer-events: none;
        }

        .demo-card:hover {
            transform: translateY(-8px) rotateX(8deg) rotateY(8deg) scale(1.02);
            box-shadow:
                0 35px 70px rgba(0,0,0,0.3),
                0 25px 45px rgba(0,0,0,0.2),
                inset 0 2px 0 rgba(255,255,255,0.95),
                inset 0 -2px 0 rgba(0,0,0,0.15),
                inset 2px 0 0 rgba(255,255,255,0.6),
                inset -2px 0 0 rgba(0,0,0,0.1);
        }

        /* 卡片颜色变体 */
        .demo-card:nth-child(1) { background: linear-gradient(145deg, #ff9a9e, #fecfef); }
        .demo-card:nth-child(2) { background: linear-gradient(145deg, #a8edea, #fed6e3); }
        .demo-card:nth-child(3) { background: linear-gradient(145deg, #ffecd2, #fcb69f); }
        .demo-card:nth-child(4) { background: linear-gradient(145deg, #a8e6cf, #dcedc1); }
        .demo-card:nth-child(5) { background: linear-gradient(145deg, #ffd3a5, #fd9853); }
        .demo-card:nth-child(6) { background: linear-gradient(145deg, #c2e9fb, #a1c4fd); }
        .demo-card:nth-child(7) { background: linear-gradient(145deg, #f093fb, #f5576c); }
        .demo-card:nth-child(8) { background: linear-gradient(145deg, #4facfe, #00f2fe); }
        .demo-card:nth-child(9) { background: linear-gradient(145deg, #43e97b, #38f9d7); }

        .card-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.9);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            box-shadow:
                0 8px 16px rgba(0,0,0,0.15),
                inset 0 2px 0 rgba(255,255,255,0.8),
                inset 0 -2px 0 rgba(0,0,0,0.1);
            border: 2px solid rgba(255,255,255,0.8);
            position: relative;
        }

        .card-icon::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            border-radius: 16px;
            background: linear-gradient(145deg, rgba(255,255,255,0.3), transparent);
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 800;
            color: rgba(0,0,0,0.8);
            margin-bottom: 0.5rem;
            text-shadow: 0 1px 2px rgba(255,255,255,0.8);
        }

        .card-description {
            display: none; /* 隐藏描述，保持简洁 */
        }

        .card-actions {
            display: none; /* 隐藏按钮，保持简洁 */
        }

        .btn {
            padding: 0.7rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        /* 添加点击效果 */
        .demo-card:active {
            transform: translateY(-5px) rotateX(4deg) rotateY(4deg) scale(0.98);
            transition: all 0.1s ease;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .cards-grid {
                grid-template-columns: repeat(2, 1fr);
                max-width: 600px;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
            }

            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 0.5rem;
            }

            .nav-link {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }

            .main-container {
                padding: 2rem 1rem;
            }

            .page-title {
                font-size: 2.5rem;
            }

            .cards-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
                max-width: 400px;
            }

            .demo-card {
                border-radius: 20px;
            }

            .card-icon {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }

            .card-title {
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .cards-grid {
                grid-template-columns: 1fr;
                max-width: 280px;
            }

            .page-title {
                font-size: 2rem;
            }

            .page-subtitle {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">演示站点</div>
            <ul class="nav-links">
                <li><a href="#" class="nav-link">首页</a></li>
                <li><a href="#" class="nav-link">论坛</a></li>
                <li><a href="#" class="nav-link">联系我们</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="main-container">
        <h1 class="page-title">系统演示中心</h1>
        <p class="page-subtitle">探索我们的各种演示系统，体验创新技术的魅力</p>

        <!-- 演示系统卡片网格 -->
        <div class="cards-grid">
            <!-- 管理系统演示 -->
            <div class="demo-card" onclick="openDemo('management')">
                <div class="card-icon">🏢</div>
                <h3 class="card-title">企业管理</h3>
            </div>

            <!-- 电商系统演示 -->
            <div class="demo-card" onclick="openDemo('ecommerce')">
                <div class="card-icon">🛒</div>
                <h3 class="card-title">电商平台</h3>
            </div>

            <!-- 数据分析系统 -->
            <div class="demo-card" onclick="openDemo('analytics')">
                <div class="card-icon">📊</div>
                <h3 class="card-title">数据分析</h3>
            </div>

            <!-- 内容管理系统 -->
            <div class="demo-card" onclick="openDemo('cms')">
                <div class="card-icon">📝</div>
                <h3 class="card-title">内容管理</h3>
            </div>

            <!-- 在线教育平台 -->
            <div class="demo-card" onclick="openDemo('education')">
                <div class="card-icon">🎓</div>
                <h3 class="card-title">在线教育</h3>
            </div>

            <!-- 项目管理工具 -->
            <div class="demo-card" onclick="openDemo('project')">
                <div class="card-icon">📋</div>
                <h3 class="card-title">项目管理</h3>
            </div>

            <!-- 客服系统 -->
            <div class="demo-card" onclick="openDemo('support')">
                <div class="card-icon">💬</div>
                <h3 class="card-title">客服系统</h3>
            </div>

            <!-- 财务管理 -->
            <div class="demo-card" onclick="openDemo('finance')">
                <div class="card-icon">💰</div>
                <h3 class="card-title">财务管理</h3>
            </div>

            <!-- 人力资源 -->
            <div class="demo-card" onclick="openDemo('hr')">
                <div class="card-icon">👥</div>
                <h3 class="card-title">人力资源</h3>
            </div>
        </div>
    </div>

    <script>
        function openDemo(type) {
            // 添加点击反馈效果
            event.target.closest('.demo-card').style.transform = 'translateY(-5px) rotateX(4deg) rotateY(4deg) scale(0.95)';

            setTimeout(() => {
                // 这里可以添加跳转到具体演示系统的逻辑
                console.log('打开演示系统:', type);
                alert(`即将打开 ${getSystemName(type)} 演示系统`);

                // 恢复卡片状态
                event.target.closest('.demo-card').style.transform = '';
            }, 150);
        }

        function getSystemName(type) {
            const names = {
                'management': '企业管理',
                'ecommerce': '电商平台',
                'analytics': '数据分析',
                'cms': '内容管理',
                'education': '在线教育',
                'project': '项目管理',
                'support': '客服系统',
                'finance': '财务管理',
                'hr': '人力资源'
            };
            return names[type] || '未知系统';
        }

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
