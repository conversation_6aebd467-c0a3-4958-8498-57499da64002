<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示站点 - 系统入口</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #e8e8e8 0%, #f5f5f5 100%);
            min-height: 100vh;
            overflow-x: hidden;
            color: #333333;
            padding: 2rem;
        }

        /* 背景纹理 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 30% 30%, rgba(0, 0, 0, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(0, 0, 0, 0.01) 0%, transparent 50%);
            z-index: -1;
            pointer-events: none;
        }

        /* 导航栏 */
        .navbar {
            display: none; /* 隐藏导航栏，专注于卡片展示 */
        }

        /* 主要内容区域 */
        .main-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem 0;
            position: relative;
        }

        .page-title {
            text-align: center;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #333333;
            letter-spacing: -0.5px;
        }

        .page-subtitle {
            text-align: center;
            color: #666666;
            font-size: 1rem;
            margin-bottom: 3rem;
            font-weight: 400;
            line-height: 1.6;
        }

        /* 卡片网格 - 2x2布局 */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            margin-top: 2rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* 航班卡片样式 - 参考图片风格 */
        .demo-card {
            background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
            border-radius: 24px;
            padding: 2rem;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 8px 16px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
            overflow: hidden;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
            border-radius: 24px;
            pointer-events: none;
        }

        .demo-card:hover {
            transform: translateY(-8px);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.4),
                0 12px 24px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        /* 航班信息卡片内容样式 */
        .flight-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
        }

        .flight-route {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .airport-code {
            font-size: 2rem;
            font-weight: 700;
            color: #ffffff;
            letter-spacing: -1px;
        }

        .arrow {
            color: #9acd32;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .city-name {
            font-size: 0.9rem;
            color: #888888;
            margin-top: 0.2rem;
            font-weight: 500;
        }

        .flight-time {
            font-size: 0.8rem;
            color: #666666;
            margin-top: 0.3rem;
        }

        .eta-info {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 0.8rem 1rem;
            text-align: right;
        }

        .eta-label {
            font-size: 0.7rem;
            color: #888888;
            margin-bottom: 0.2rem;
        }

        .eta-time {
            font-size: 1rem;
            color: #ffffff;
            font-weight: 600;
        }

        .flight-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;
        }

        .progress-bar {
            background: linear-gradient(90deg, #9acd32 0%, #7cb342 100%);
            height: 40px;
            border-radius: 20px;
            flex: 1;
            margin-right: 1rem;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(154, 205, 50, 0.3);
        }

        .plane-icon {
            color: #ffffff;
            font-size: 1.2rem;
            background: rgba(255, 255, 255, 0.2);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .time-info {
            font-size: 0.8rem;
            color: #666666;
            text-align: right;
        }

        .dinner-info {
            color: #ff9500;
            font-weight: 600;
            font-size: 0.8rem;
        }

        /* 隐藏不需要的元素 */
        .card-icon,
        .card-title,
        .card-description,
        .card-actions {
            display: none;
        }

        .btn {
            padding: 0.7rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        /* 添加点击效果 */
        .demo-card:active {
            transform: translateY(-4px) scale(0.98);
            transition: all 0.1s ease;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .main-container {
                padding: 1rem 0;
                max-width: 100%;
            }

            .page-title {
                font-size: 1.8rem;
            }

            .page-subtitle {
                font-size: 0.9rem;
            }

            .cards-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                max-width: 400px;
            }

            .demo-card {
                min-height: 180px;
                padding: 1.5rem;
            }

            .airport-code {
                font-size: 1.5rem;
            }

            .flight-route {
                gap: 0.8rem;
            }

            .eta-info {
                padding: 0.6rem 0.8rem;
            }

            .progress-bar {
                height: 35px;
                margin-right: 0.8rem;
            }

            .plane-icon {
                width: 28px;
                height: 28px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .cards-grid {
                max-width: 320px;
            }

            .demo-card {
                min-height: 160px;
                padding: 1.2rem;
            }

            .airport-code {
                font-size: 1.3rem;
            }

            .city-name {
                font-size: 0.8rem;
            }

            .flight-time {
                font-size: 0.7rem;
            }

            .eta-info {
                padding: 0.5rem 0.6rem;
            }

            .eta-time {
                font-size: 0.9rem;
            }

            .dinner-info {
                font-size: 0.7rem;
            }

            .progress-bar {
                height: 30px;
            }

            .plane-icon {
                width: 24px;
                height: 24px;
                font-size: 0.9rem;
            }

            .time-info {
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">演示站点</div>
            <ul class="nav-links">
                <li><a href="#" class="nav-link">首页</a></li>
                <li><a href="#" class="nav-link">论坛</a></li>
                <li><a href="#" class="nav-link">联系我们</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="main-container">
        <h1 class="page-title">Flight Dashboard</h1>
        <p class="page-subtitle">Modern flight information display system</p>

        <!-- 航班信息卡片网格 -->
        <div class="cards-grid">
            <!-- 航班1: 多伦多到东京 -->
            <div class="demo-card" onclick="openDemo('flight1')">
                <div class="flight-header">
                    <div class="flight-route">
                        <div>
                            <div class="airport-code">YYZ</div>
                            <div class="city-name">TORONTO</div>
                            <div class="flight-time">MON, 6:14 PM</div>
                        </div>
                        <div class="arrow">→</div>
                        <div>
                            <div class="airport-code">HND</div>
                            <div class="city-name">TOKYO</div>
                            <div class="flight-time">TUE, 7:14 AM</div>
                        </div>
                    </div>
                    <div class="eta-info">
                        <div class="eta-label">ETA 2:15 PM</div>
                        <div class="eta-time">Tokyo Time</div>
                        <div class="dinner-info">DINNER IN 2:34H</div>
                    </div>
                </div>
                <div class="flight-details">
                    <div class="progress-bar">
                        <div class="plane-icon">✈</div>
                    </div>
                    <div class="time-info">-7H 01M</div>
                </div>
            </div>

            <!-- 航班2: 纽约到伦敦 -->
            <div class="demo-card" onclick="openDemo('flight2')">
                <div class="flight-header">
                    <div class="flight-route">
                        <div>
                            <div class="airport-code">JFK</div>
                            <div class="city-name">NEW YORK</div>
                            <div class="flight-time">TUE, 11:30 PM</div>
                        </div>
                        <div class="arrow">→</div>
                        <div>
                            <div class="airport-code">LHR</div>
                            <div class="city-name">LONDON</div>
                            <div class="flight-time">WED, 10:45 AM</div>
                        </div>
                    </div>
                    <div class="eta-info">
                        <div class="eta-label">ETA 10:45 AM</div>
                        <div class="eta-time">London Time</div>
                        <div class="dinner-info">BREAKFAST IN 1:15H</div>
                    </div>
                </div>
                <div class="flight-details">
                    <div class="progress-bar">
                        <div class="plane-icon">✈</div>
                    </div>
                    <div class="time-info">-6H 15M</div>
                </div>
            </div>

            <!-- 航班3: 洛杉矶到悉尼 -->
            <div class="demo-card" onclick="openDemo('flight3')">
                <div class="flight-header">
                    <div class="flight-route">
                        <div>
                            <div class="airport-code">LAX</div>
                            <div class="city-name">LOS ANGELES</div>
                            <div class="flight-time">WED, 2:20 AM</div>
                        </div>
                        <div class="arrow">→</div>
                        <div>
                            <div class="airport-code">SYD</div>
                            <div class="city-name">SYDNEY</div>
                            <div class="flight-time">THU, 9:35 AM</div>
                        </div>
                    </div>
                    <div class="eta-info">
                        <div class="eta-label">ETA 9:35 AM</div>
                        <div class="eta-time">Sydney Time</div>
                        <div class="dinner-info">LUNCH IN 4:20H</div>
                    </div>
                </div>
                <div class="flight-details">
                    <div class="progress-bar">
                        <div class="plane-icon">✈</div>
                    </div>
                    <div class="time-info">-14H 15M</div>
                </div>
            </div>

            <!-- 航班4: 巴黎到迪拜 -->
            <div class="demo-card" onclick="openDemo('flight4')">
                <div class="flight-header">
                    <div class="flight-route">
                        <div>
                            <div class="airport-code">CDG</div>
                            <div class="city-name">PARIS</div>
                            <div class="flight-time">THU, 3:45 PM</div>
                        </div>
                        <div class="arrow">→</div>
                        <div>
                            <div class="airport-code">DXB</div>
                            <div class="city-name">DUBAI</div>
                            <div class="flight-time">THU, 11:20 PM</div>
                        </div>
                    </div>
                    <div class="eta-info">
                        <div class="eta-label">ETA 11:20 PM</div>
                        <div class="eta-time">Dubai Time</div>
                        <div class="dinner-info">DINNER IN 1:45H</div>
                    </div>
                </div>
                <div class="flight-details">
                    <div class="progress-bar">
                        <div class="plane-icon">✈</div>
                    </div>
                    <div class="time-info">-6H 35M</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openDemo(type) {
            // 添加点击反馈效果
            event.target.closest('.demo-card').style.transform = 'translateY(-5px) rotateX(4deg) rotateY(4deg) scale(0.95)';

            setTimeout(() => {
                // 这里可以添加跳转到具体航班详情的逻辑
                console.log('查看航班详情:', type);
                alert(`正在查看 ${getSystemName(type)} 航班详情`);

                // 恢复卡片状态
                event.target.closest('.demo-card').style.transform = '';
            }, 150);
        }

        function getSystemName(type) {
            const names = {
                'flight1': 'YYZ → HND (Toronto to Tokyo)',
                'flight2': 'JFK → LHR (New York to London)',
                'flight3': 'LAX → SYD (Los Angeles to Sydney)',
                'flight4': 'CDG → DXB (Paris to Dubai)'
            };
            return names[type] || 'Unknown Flight';
        }

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
