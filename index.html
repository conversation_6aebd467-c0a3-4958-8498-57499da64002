<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示站点 - 系统入口</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
            min-height: 100vh;
            perspective: 1000px;
            overflow-x: hidden;
            color: #ffffff;
        }

        /* 背景纹理 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.01) 0%, transparent 50%);
            z-index: -1;
            pointer-events: none;
        }

        /* 导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(40px);
            -webkit-backdrop-filter: blur(40px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 1px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            letter-spacing: -0.5px;
        }

        .nav-links {
            display: flex;
            gap: 0.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 0.6rem 1.2rem;
            border-radius: 12px;
            transition: all 0.2s ease;
            font-size: 0.9rem;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .nav-link:hover {
            color: rgba(255, 255, 255, 0.95);
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        /* 主要内容区域 */
        .main-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 3rem 2rem;
            position: relative;
        }

        .page-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: rgba(255, 255, 255, 0.95);
            letter-spacing: -1px;
        }

        .page-subtitle {
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 1rem;
            margin-bottom: 3rem;
            font-weight: 400;
            line-height: 1.6;
        }

        /* 卡片网格 - 不规则布局 */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(4, 120px);
            gap: 1rem;
            margin-top: 2rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* 主卡片 - 占据左上角大区域 */
        .demo-card:nth-child(1) {
            grid-column: 1 / 3;
            grid-row: 1 / 3;
        }

        /* 其他卡片的特殊布局 */
        .demo-card:nth-child(2) {
            grid-column: 3 / 5;
            grid-row: 1 / 2;
        }

        .demo-card:nth-child(3) {
            grid-column: 3 / 5;
            grid-row: 2 / 3;
        }

        .demo-card:nth-child(4) {
            grid-column: 1 / 2;
            grid-row: 3 / 4;
        }

        .demo-card:nth-child(5) {
            grid-column: 2 / 4;
            grid-row: 3 / 4;
        }

        .demo-card:nth-child(6) {
            grid-column: 4 / 5;
            grid-row: 3 / 5;
        }

        .demo-card:nth-child(7) {
            grid-column: 1 / 2;
            grid-row: 4 / 5;
        }

        .demo-card:nth-child(8) {
            grid-column: 2 / 3;
            grid-row: 4 / 5;
        }

        .demo-card:nth-child(9) {
            grid-column: 3 / 4;
            grid-row: 4 / 5;
        }

        /* 毛玻璃卡片样式 - 参考图片风格 */
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.15);
            padding: 1.5rem;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.12),
                0 2px 8px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            overflow: hidden;
        }

        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            border-radius: 20px;
            pointer-events: none;
        }

        .demo-card:hover {
            transform: translateY(-4px);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.15),
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.2);
        }

        /* 主卡片特殊样式 */
        .main-card {
            background: rgba(255, 255, 255, 0.15);
            justify-content: flex-start;
            align-items: flex-start;
            text-align: left;
            padding: 2rem;
        }

        .main-card::before {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
        }

        .main-card .card-icon {
            position: absolute;
            font-size: 1.5rem;
        }

        .main-card .card-icon:first-child {
            top: 1.5rem;
            left: 1.5rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .main-card .card-icon:nth-child(2) {
            top: 1.5rem;
            right: 1.5rem;
            background: rgba(255, 255, 255, 0.9);
            color: rgba(0, 0, 0, 0.8);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .main-card .card-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-top: 4rem;
            margin-bottom: 0.5rem;
            color: rgba(255, 255, 255, 0.95);
        }

        .card-subtitle {
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.4;
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .generate-btn {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 0.8rem 2rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: auto;
        }

        .generate-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
        }

        .card-icon {
            font-size: 1.8rem;
            margin-bottom: 0.8rem;
            opacity: 0.9;
        }

        .card-title {
            font-size: 0.8rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            letter-spacing: -0.2px;
            line-height: 1.2;
        }

        /* 音乐播放器卡片特殊样式 */
        .demo-card:nth-child(2) {
            background: rgba(100, 100, 100, 0.3);
            position: relative;
        }

        .demo-card:nth-child(2)::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(200, 200, 200, 0.3) 30%, transparent 70%);
            border-radius: 50%;
        }

        .demo-card:nth-child(2) .card-title {
            font-size: 0.7rem;
            text-align: center;
            position: absolute;
            bottom: 1rem;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
        }

        /* 天气卡片特殊样式 */
        .demo-card:nth-child(3) {
            background: rgba(80, 80, 80, 0.4);
            position: relative;
        }

        .demo-card:nth-child(3)::after {
            content: '';
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, rgba(200, 200, 200, 0.4) 40%, transparent 70%);
            border-radius: 50%;
        }

        .demo-card:nth-child(3) .card-title {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 0.7rem;
            text-align: right;
        }

        .card-description {
            display: none;
        }

        .card-actions {
            display: none;
        }

        .btn {
            padding: 0.7rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        /* 添加点击效果 */
        .demo-card:active {
            transform: translateY(-2px) scale(0.98);
            transition: all 0.1s ease;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .cards-grid {
                grid-template-columns: repeat(3, 1fr);
                grid-template-rows: repeat(3, 100px);
                max-width: 600px;
            }

            .demo-card:nth-child(1) {
                grid-column: 1 / 3;
                grid-row: 1 / 3;
            }

            .demo-card:nth-child(2) {
                grid-column: 3 / 4;
                grid-row: 1 / 2;
            }

            .demo-card:nth-child(3) {
                grid-column: 3 / 4;
                grid-row: 2 / 3;
            }

            .demo-card:nth-child(4) {
                grid-column: 1 / 2;
                grid-row: 3 / 4;
            }

            .demo-card:nth-child(5) {
                grid-column: 2 / 4;
                grid-row: 3 / 4;
            }

            .demo-card:nth-child(n+6) {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 0.8rem 1rem;
            }

            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 0.3rem;
            }

            .nav-link {
                padding: 0.5rem 1rem;
                font-size: 0.85rem;
            }

            .main-container {
                padding: 2rem 1rem;
                max-width: 100%;
            }

            .page-title {
                font-size: 2rem;
            }

            .page-subtitle {
                font-size: 0.9rem;
            }

            .cards-grid {
                grid-template-columns: repeat(2, 1fr);
                grid-template-rows: repeat(3, 80px);
                gap: 0.8rem;
                max-width: 400px;
            }

            .demo-card:nth-child(1) {
                grid-column: 1 / 3;
                grid-row: 1 / 2;
            }

            .demo-card:nth-child(2) {
                grid-column: 1 / 2;
                grid-row: 2 / 3;
            }

            .demo-card:nth-child(3) {
                grid-column: 2 / 3;
                grid-row: 2 / 3;
            }

            .demo-card:nth-child(4) {
                grid-column: 1 / 2;
                grid-row: 3 / 4;
            }

            .demo-card:nth-child(5) {
                grid-column: 2 / 3;
                grid-row: 3 / 4;
            }

            .demo-card:nth-child(n+6) {
                display: none;
            }

            .card-icon {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }

            .demo-card:nth-child(1) .card-icon {
                font-size: 2rem;
            }

            .card-title {
                font-size: 0.8rem;
            }

            .demo-card:nth-child(1) .card-title {
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .cards-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(5, 70px);
                max-width: 280px;
            }

            .demo-card:nth-child(1) {
                grid-column: 1 / 2;
                grid-row: 1 / 2;
            }

            .demo-card:nth-child(2) {
                grid-column: 1 / 2;
                grid-row: 2 / 3;
            }

            .demo-card:nth-child(3) {
                grid-column: 1 / 2;
                grid-row: 3 / 4;
            }

            .demo-card:nth-child(4) {
                grid-column: 1 / 2;
                grid-row: 4 / 5;
            }

            .demo-card:nth-child(5) {
                grid-column: 1 / 2;
                grid-row: 5 / 6;
            }

            .page-title {
                font-size: 1.8rem;
            }

            .page-subtitle {
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">演示站点</div>
            <ul class="nav-links">
                <li><a href="#" class="nav-link">首页</a></li>
                <li><a href="#" class="nav-link">论坛</a></li>
                <li><a href="#" class="nav-link">联系我们</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="main-container">
        <h1 class="page-title">TamakoLab</h1>
        <p class="page-subtitle">Be simple but never flamboyant;<br>be creative but never formulaic</p>

        <!-- 演示系统卡片网格 -->
        <div class="cards-grid">
            <!-- 管理系统演示 -->
            <div class="demo-card main-card" onclick="openDemo('management')">
                <div class="card-icon">✨</div>
                <div class="card-icon">↗</div>
                <h3 class="card-title">TamakoLab</h3>
                <p class="card-subtitle">Be simple but never<br>flamboyant;<br>be creative but never<br>formulaic</p>
                <button class="generate-btn">Generate</button>
            </div>

            <!-- 音乐播放器 -->
            <div class="demo-card" onclick="openDemo('music')">
                <div class="card-icon">🎵</div>
                <h3 class="card-title">Daft Punk<br>Giorgio by Moroder</h3>
            </div>

            <!-- 天气应用 -->
            <div class="demo-card" onclick="openDemo('weather')">
                <div class="card-icon">🌙</div>
                <h3 class="card-title">18:30<br>MAY 14</h3>
            </div>

            <!-- 设计工具 -->
            <div class="demo-card" onclick="openDemo('design')">
                <div class="card-icon">✨</div>
                <h3 class="card-title">Generate</h3>
            </div>

            <!-- 收藏夹 -->
            <div class="demo-card" onclick="openDemo('favorites')">
                <div class="card-icon">♥</div>
                <h3 class="card-title">Favorites</h3>
            </div>

            <!-- 按钮组件 -->
            <div class="demo-card" onclick="openDemo('button')">
                <div class="card-icon">●</div>
                <h3 class="card-title">Button</h3>
            </div>

            <!-- 播放控制 -->
            <div class="demo-card" onclick="openDemo('play')">
                <div class="card-icon">▶</div>
                <h3 class="card-title">Play</h3>
            </div>

            <!-- 设置 -->
            <div class="demo-card" onclick="openDemo('settings')">
                <div class="card-icon">⚙</div>
                <h3 class="card-title">Settings</h3>
            </div>

            <!-- 通知 -->
            <div class="demo-card" onclick="openDemo('notifications')">
                <div class="card-icon">🔔</div>
                <h3 class="card-title">Alerts</h3>
            </div>
        </div>
    </div>

    <script>
        function openDemo(type) {
            // 添加点击反馈效果
            event.target.closest('.demo-card').style.transform = 'translateY(-5px) rotateX(4deg) rotateY(4deg) scale(0.95)';

            setTimeout(() => {
                // 这里可以添加跳转到具体演示系统的逻辑
                console.log('打开演示系统:', type);
                alert(`即将打开 ${getSystemName(type)} 演示系统`);

                // 恢复卡片状态
                event.target.closest('.demo-card').style.transform = '';
            }, 150);
        }

        function getSystemName(type) {
            const names = {
                'management': 'TamakoLab',
                'music': 'Music Player',
                'weather': 'Weather App',
                'design': 'Design Tool',
                'favorites': 'Favorites',
                'button': 'Button Component',
                'play': 'Media Player',
                'settings': 'Settings',
                'notifications': 'Notifications'
            };
            return names[type] || 'Unknown System';
        }

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
