<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示站点 - 系统入口</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            perspective: 1000px;
        }

        /* 导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        /* 主要内容区域 */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 3rem 2rem;
        }

        .page-title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            margin-bottom: 3rem;
        }

        /* 卡片网格 */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        /* 3D写实卡片样式 */
        .demo-card {
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.1),
                0 15px 25px rgba(0,0,0,0.05),
                inset 0 1px 0 rgba(255,255,255,0.8),
                inset 0 -1px 0 rgba(0,0,0,0.1);
            transform: translateZ(0);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 20px 20px 0 0;
        }

        .demo-card:hover {
            transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
            box-shadow: 
                0 30px 60px rgba(0,0,0,0.15),
                0 20px 35px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.9),
                inset 0 -1px 0 rgba(0,0,0,0.1);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }

        .card-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 0.8rem;
        }

        .card-description {
            color: #718096;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .card-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.7rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
            }
            
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                gap: 1rem;
            }
            
            .main-container {
                padding: 2rem 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">演示站点</div>
            <ul class="nav-links">
                <li><a href="#" class="nav-link">首页</a></li>
                <li><a href="#" class="nav-link">论坛</a></li>
                <li><a href="#" class="nav-link">联系我们</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="main-container">
        <h1 class="page-title">系统演示中心</h1>
        <p class="page-subtitle">探索我们的各种演示系统，体验创新技术的魅力</p>

        <!-- 演示系统卡片网格 -->
        <div class="cards-grid">
            <!-- 管理系统演示 -->
            <div class="demo-card">
                <div class="card-icon">🏢</div>
                <h3 class="card-title">企业管理系统</h3>
                <p class="card-description">完整的企业级管理解决方案，包含用户管理、权限控制、数据分析等核心功能模块。</p>
                <div class="card-actions">
                    <a href="#" class="btn btn-primary">立即体验</a>
                    <a href="#" class="btn btn-secondary">查看详情</a>
                </div>
            </div>

            <!-- 电商系统演示 -->
            <div class="demo-card">
                <div class="card-icon">🛒</div>
                <h3 class="card-title">电商购物平台</h3>
                <p class="card-description">现代化的电商解决方案，支持商品管理、订单处理、支付集成和用户体验优化。</p>
                <div class="card-actions">
                    <a href="#" class="btn btn-primary">立即体验</a>
                    <a href="#" class="btn btn-secondary">查看详情</a>
                </div>
            </div>

            <!-- 数据分析系统 -->
            <div class="demo-card">
                <div class="card-icon">📊</div>
                <h3 class="card-title">数据分析平台</h3>
                <p class="card-description">强大的数据可视化和分析工具，帮助企业从数据中获取有价值的商业洞察。</p>
                <div class="card-actions">
                    <a href="#" class="btn btn-primary">立即体验</a>
                    <a href="#" class="btn btn-secondary">查看详情</a>
                </div>
            </div>

            <!-- 内容管理系统 -->
            <div class="demo-card">
                <div class="card-icon">📝</div>
                <h3 class="card-title">内容管理系统</h3>
                <p class="card-description">灵活的CMS解决方案，支持多媒体内容管理、SEO优化和多语言支持。</p>
                <div class="card-actions">
                    <a href="#" class="btn btn-primary">立即体验</a>
                    <a href="#" class="btn btn-secondary">查看详情</a>
                </div>
            </div>

            <!-- 在线教育平台 -->
            <div class="demo-card">
                <div class="card-icon">🎓</div>
                <h3 class="card-title">在线教育平台</h3>
                <p class="card-description">完整的在线学习解决方案，包含课程管理、学习跟踪、互动讨论等功能。</p>
                <div class="card-actions">
                    <a href="#" class="btn btn-primary">立即体验</a>
                    <a href="#" class="btn btn-secondary">查看详情</a>
                </div>
            </div>

            <!-- 项目管理工具 -->
            <div class="demo-card">
                <div class="card-icon">📋</div>
                <h3 class="card-title">项目管理工具</h3>
                <p class="card-description">高效的项目协作平台，支持任务分配、进度跟踪、团队协作和资源管理。</p>
                <div class="card-actions">
                    <a href="#" class="btn btn-primary">立即体验</a>
                    <a href="#" class="btn btn-secondary">查看详情</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
